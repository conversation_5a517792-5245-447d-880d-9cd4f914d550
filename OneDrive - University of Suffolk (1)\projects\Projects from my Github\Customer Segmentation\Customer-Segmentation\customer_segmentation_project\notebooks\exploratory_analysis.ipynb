import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Set style for plots
plt.style.use('seaborn')
sns.set_palette('viridis')

# Load the data
df = pd.read_csv('../data/sample_customer_data.csv')

# Display basic information
print(df.info())
df.head()

# Pairplot for feature relationships
sns.pairplot(df[['purchase_frequency', 'total_spend', 'avg_order_value', 'customer_lifetime']])
plt.show()

# Correlation heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(df[['purchase_frequency', 'total_spend', 'avg_order_value', 'customer_lifetime']].corr(), annot=True, cmap='coolwarm')
plt.title('Correlation Heatmap of Customer Features')
plt.show()

# Load segmented data
segmented_df = pd.read_csv('../results/segmented_customers.csv')

# Visualize KMeans clusters
plt.figure(figsize=(12, 8))
sns.scatterplot(data=segmented_df, x='total_spend', y='purchase_frequency', hue='KMeans_Cluster', palette='viridis')
plt.title('Customer Segments: Total Spend vs Purchase Frequency (KMeans)')
plt.show()

# Visualize DBSCAN clusters
plt.figure(figsize=(12, 8))
sns.scatterplot(data=segmented_df, x='avg_order_value', y='customer_lifetime', hue='DBSCAN_Cluster', palette='viridis')
plt.title('Customer Segments: Average Order Value vs Customer Lifetime (DBSCAN)')
plt.show()

# Profile KMeans clusters
kmeans_profile = segmented_df.groupby('KMeans_Cluster').mean()
print('KMeans Cluster Profiles:
', kmeans_profile)

# Profile DBSCAN clusters
dbscan_profile = segmented_df.groupby('DBSCAN_Cluster').mean()
print('
DBSCAN Cluster Profiles:
', dbscan_profile)