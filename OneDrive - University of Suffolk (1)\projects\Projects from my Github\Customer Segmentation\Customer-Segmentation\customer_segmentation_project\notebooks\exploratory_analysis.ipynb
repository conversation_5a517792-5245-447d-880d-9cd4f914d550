{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Customer Segmentation: Exploratory Data Analysis\n", "\n", "This notebook contains exploratory data analysis and visualization for the customer segmentation project."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Set style for plots\n", "plt.style.use('seaborn')\n", "sns.set_palette('viridis')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load and Inspect Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Load the data\n", "df = pd.read_csv('../data/sample_customer_data.csv')\n", "\n", "# Display basic information\n", "print(df.info())\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Distribution and Correlation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Pairplot for feature relationships\n", "sns.pairplot(df[['purchase_frequency', 'total_spend', 'avg_order_value', 'customer_lifetime']])\n", "plt.show()\n", "\n", "# Correlation heatmap\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(df[['purchase_frequency', 'total_spend', 'avg_order_value', 'customer_lifetime']].corr(), annot=True, cmap='coolwarm')\n", "plt.title('Correlation Heatmap of Customer Features')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cluster Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Load segmented data\n", "segmented_df = pd.read_csv('../results/segmented_customers.csv')\n", "\n", "# Visualize KMeans clusters\n", "plt.figure(figsize=(12, 8))\n", "sns.scatterplot(data=segmented_df, x='total_spend', y='purchase_frequency', hue='KMeans_Cluster', palette='viridis')\n", "plt.title('Customer Segments: Total Spend vs Purchase Frequency (KMeans)')\n", "plt.show()\n", "\n", "# Visualize DBSCAN clusters\n", "plt.figure(figsize=(12, 8))\n", "sns.scatterplot(data=segmented_df, x='avg_order_value', y='customer_lifetime', hue='DBSCAN_Cluster', palette='viridis')\n", "plt.title('Customer Segments: Average Order Value vs Customer Lifetime (DBSCAN)')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cluster Profiling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Profile KMeans clusters\n", "kmeans_profile = segmented_df.groupby('KMeans_Cluster').mean()\n", "print('KMeans Cluster Profiles:\n', kmeans_profile)\n", "\n", "# Profile DBSCAN clusters\n", "dbscan_profile = segmented_df.groupby('DBSCAN_Cluster').mean()\n", "print('\nDBSCAN Cluster Profiles:\n', dbscan_profile)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}