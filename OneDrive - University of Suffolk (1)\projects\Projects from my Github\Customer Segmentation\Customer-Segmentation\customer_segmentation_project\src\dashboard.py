import dash
from dash import dcc, html, Input, Output
import plotly.express as px
import pandas as pd
import numpy as np
import os

# Initialize the Dash app
app = dash.Dash(__name__)

# Function to load actual customer data
def load_data():
    # Try to load the actual customer data first
    try:
        # Get the path to the customer data file
        current_dir = os.path.dirname(os.path.abspath(__file__))
        data_path = os.path.join(current_dir, '..', '..', 'customer_data.csv')

        if os.path.exists(data_path):
            df = pd.read_csv(data_path)
            # Add some dummy segments for visualization
            np.random.seed(42)  # For reproducible results
            df['Segment'] = np.random.choice(['High Value', 'Medium Value', 'Low Value', 'New Customer'], len(df))
            df['Spending'] = df['Spending Score (1-100)'] * 10  # Scale up for better visualization
            return df
    except Exception as e:
        print(f"Error loading data: {e}")

    # Fallback to dummy data
    np.random.seed(42)
    return pd.DataFrame({
        'CustomerID': range(1, 101),
        'Age': np.random.randint(18, 70, 100),
        'Annual Income (k$)': np.random.randint(15, 140, 100),
        'Spending Score (1-100)': np.random.randint(1, 100, 100),
        'Segment': np.random.choice(['High Value', 'Medium Value', 'Low Value', 'New Customer'], 100),
        'Spending': np.random.randint(100, 1000, 100)
    })

# Layout of the dashboard
app.layout = html.Div([
    html.H1('Customer Segmentation Dashboard', style={'textAlign': 'center', 'color': '#2c3e50'}),

    html.Div([
        html.Div([
            dcc.Graph(id='segment-distribution')
        ], className='six columns'),

        html.Div([
            dcc.Graph(id='spending-by-segment')
        ], className='six columns'),
    ], className='row'),

    html.Div([
        html.Div([
            dcc.Graph(id='age-income-scatter')
        ], className='six columns'),

        html.Div([
            dcc.Graph(id='spending-score-histogram')
        ], className='six columns'),
    ], className='row'),

    dcc.Interval(
        id='interval-component',
        interval=60*1000,  # in milliseconds, update every 1 minute
        n_intervals=0
    )
], style={'margin': '20px'})

@app.callback(
    [Output('segment-distribution', 'figure'),
     Output('spending-by-segment', 'figure'),
     Output('age-income-scatter', 'figure'),
     Output('spending-score-histogram', 'figure')],
    [Input('interval-component', 'n_intervals')]
)
def update_graphs(n):
    df = load_data()

    # Segment distribution pie chart
    segment_dist = px.pie(df, names='Segment', title='Customer Segment Distribution',
                         color_discrete_sequence=px.colors.qualitative.Set3)

    # Spending by segment box plot
    spending_by_segment = px.box(df, x='Segment', y='Spending', title='Spending Distribution by Segment',
                                color='Segment', color_discrete_sequence=px.colors.qualitative.Set3)

    # Age vs Income scatter plot
    age_income_scatter = px.scatter(df, x='Age', y='Annual Income (k$)', color='Segment',
                                   title='Age vs Annual Income by Segment',
                                   color_discrete_sequence=px.colors.qualitative.Set3)

    # Spending Score histogram
    spending_score_hist = px.histogram(df, x='Spending Score (1-100)', color='Segment',
                                      title='Spending Score Distribution by Segment',
                                      color_discrete_sequence=px.colors.qualitative.Set3)

    return segment_dist, spending_by_segment, age_income_scatter, spending_score_hist

if __name__ == '__main__':
    app.run_server(debug=True)
