import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

%matplotlib inline
plt.style.use('seaborn')
sns.set_palette('Set2')

# Load the data
df = pd.read_csv('../data/customer_data.csv')

# Display basic information
print(df.info())
print('\nSample data:')
display(df.head())

# Basic statistics
print('\nBasic statistics:')
display(df.describe())

# Histograms for each feature
df.hist(figsize=(12, 8))
plt.tight_layout()
plt.show()

# Box plots for each feature
fig, axes = plt.subplots(1, 3, figsize=(15, 5))
sns.boxplot(data=df, y='Age', ax=axes[0])
sns.boxplot(data=df, y='Annual Income (k$)', ax=axes[1])
sns.boxplot(data=df, y='Spending Score (1-100)', ax=axes[2])
plt.tight_layout()
plt.show()

# Scatter plots
fig, axes = plt.subplots(1, 3, figsize=(18, 5))
sns.scatterplot(data=df, x='Age', y='Annual Income (k$)', ax=axes[0])
sns.scatterplot(data=df, x='Age', y='Spending Score (1-100)', ax=axes[1])
sns.scatterplot(data=df, x='Annual Income (k$)', y='Spending Score (1-100)', ax=axes[2])
plt.tight_layout()
plt.show()

# Correlation matrix
corr_matrix = df.corr()
plt.figure(figsize=(10, 8))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', vmin=-1, vmax=1, center=0)
plt.title('Correlation Matrix')
plt.show()