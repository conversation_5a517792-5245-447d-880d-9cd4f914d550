{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exploratory Data Analysis for Customer Segmentation\n", "\n", "This notebook performs an exploratory data analysis on the customer dataset used for segmentation."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "OSError", "evalue": "'seaborn' is not a valid package style, path of style file, URL of style file, or library style name (library styles are listed in `style.available`)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Python311\\Lib\\site-packages\\matplotlib\\style\\core.py:137\u001b[0m, in \u001b[0;36muse\u001b[1;34m(style)\u001b[0m\n\u001b[0;32m    136\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 137\u001b[0m     style \u001b[38;5;241m=\u001b[39m \u001b[43m_rc_params_in_file\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstyle\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    138\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mc:\\Python311\\Lib\\site-packages\\matplotlib\\__init__.py:879\u001b[0m, in \u001b[0;36m_rc_params_in_file\u001b[1;34m(fname, transform, fail_on_error)\u001b[0m\n\u001b[0;32m    878\u001b[0m rc_temp \u001b[38;5;241m=\u001b[39m {}\n\u001b[1;32m--> 879\u001b[0m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_open_file_or_url\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfname\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mas\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mfd\u001b[49m\u001b[43m:\u001b[49m\n\u001b[0;32m    880\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mtry\u001b[39;49;00m\u001b[43m:\u001b[49m\n", "File \u001b[1;32mc:\\Python311\\Lib\\contextlib.py:137\u001b[0m, in \u001b[0;36m_GeneratorContextManager.__enter__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    136\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 137\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mnext\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgen)\n\u001b[0;32m    138\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m:\n", "File \u001b[1;32mc:\\Python311\\Lib\\site-packages\\matplotlib\\__init__.py:856\u001b[0m, in \u001b[0;36m_open_file_or_url\u001b[1;34m(fname)\u001b[0m\n\u001b[0;32m    855\u001b[0m fname \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexpanduser(fname)\n\u001b[1;32m--> 856\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(fname, encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[0;32m    857\u001b[0m     \u001b[38;5;28;01myield\u001b[39;00m f\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'seaborn'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mOSError\u001b[0m                                   Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 7\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01msns\u001b[39;00m\n\u001b[0;32m      6\u001b[0m get_ipython()\u001b[38;5;241m.\u001b[39mrun_line_magic(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmatplotlib\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124minline\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m----> 7\u001b[0m \u001b[43mplt\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msty<PERSON>\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43muse\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43ms<PERSON>born\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m sns\u001b[38;5;241m.\u001b[39mset_palette(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSet2\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Python311\\Lib\\site-packages\\matplotlib\\style\\core.py:139\u001b[0m, in \u001b[0;36muse\u001b[1;34m(style)\u001b[0m\n\u001b[0;32m    137\u001b[0m         style \u001b[38;5;241m=\u001b[39m _rc_params_in_file(style)\n\u001b[0;32m    138\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m--> 139\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m(\n\u001b[0;32m    140\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mstyle\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[38;5;124m is not a valid package style, path of style \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    141\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfile, URL of style file, or library style name (library \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    142\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstyles are listed in `style.available`)\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m    143\u001b[0m filtered \u001b[38;5;241m=\u001b[39m {}\n\u001b[0;32m    144\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m k \u001b[38;5;129;01min\u001b[39;00m style:  \u001b[38;5;66;03m# don't trigger RcParams.__getitem__('backend')\u001b[39;00m\n", "\u001b[1;31mOSError\u001b[0m: 'seaborn' is not a valid package style, path of style file, URL of style file, or library style name (library styles are listed in `style.available`)"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "%matplotlib inline\n", "plt.style.use('seaborn')\n", "sns.set_palette('Set2')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Basic Information"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 100 entries, 0 to 99\n", "Data columns (total 4 columns):\n", " #   Column                  Non-Null Count  Dtype\n", "---  ------                  --------------  -----\n", " 0   CustomerID              100 non-null    int64\n", " 1   Age                     100 non-null    int64\n", " 2   Annual Income (k$)      100 non-null    int64\n", " 3   Spending Score (1-100)  100 non-null    int64\n", "dtypes: int64(4)\n", "memory usage: 3.2 KB\n", "None\n", "\n", "Sample data:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CustomerID</th>\n", "      <th>Age</th>\n", "      <th>Annual Income (k$)</th>\n", "      <th>Spending Score (1-100)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>19</td>\n", "      <td>15</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>21</td>\n", "      <td>15</td>\n", "      <td>81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>20</td>\n", "      <td>16</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>23</td>\n", "      <td>16</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>31</td>\n", "      <td>17</td>\n", "      <td>40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   CustomerID  Age  Annual Income (k$)  Spending Score (1-100)\n", "0           1   19                  15                      39\n", "1           2   21                  15                      81\n", "2           3   20                  16                       6\n", "3           4   23                  16                      77\n", "4           5   31                  17                      40"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Basic statistics:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CustomerID</th>\n", "      <th>Age</th>\n", "      <th>Annual Income (k$)</th>\n", "      <th>Spending Score (1-100)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>50.500000</td>\n", "      <td>39.750000</td>\n", "      <td>39.560000</td>\n", "      <td>49.930000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>29.011492</td>\n", "      <td>15.626561</td>\n", "      <td>14.212187</td>\n", "      <td>21.656294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>18.000000</td>\n", "      <td>15.000000</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>25.750000</td>\n", "      <td>24.750000</td>\n", "      <td>28.000000</td>\n", "      <td>41.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>50.500000</td>\n", "      <td>36.500000</td>\n", "      <td>41.000000</td>\n", "      <td>50.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>75.250000</td>\n", "      <td>50.250000</td>\n", "      <td>54.000000</td>\n", "      <td>60.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>100.000000</td>\n", "      <td>70.000000</td>\n", "      <td>61.000000</td>\n", "      <td>99.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       CustomerID         Age  Annual Income (k$)  Spending Score (1-100)\n", "count  100.000000  100.000000          100.000000              100.000000\n", "mean    50.500000   39.750000           39.560000               49.930000\n", "std     29.011492   15.626561           14.212187               21.656294\n", "min      1.000000   18.000000           15.000000                3.000000\n", "25%     25.750000   24.750000           28.000000               41.000000\n", "50%     50.500000   36.500000           41.000000               50.000000\n", "75%     75.250000   50.250000           54.000000               60.000000\n", "max    100.000000   70.000000           61.000000               99.000000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load the data\n", "df = pd.read_csv('../data/customer_data.csv')\n", "\n", "# Display basic information\n", "print(df.info())\n", "print('\\nSample data:')\n", "display(df.head())\n", "\n", "# Basic statistics\n", "print('\\nBasic statistics:')\n", "display(df.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Visualization"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Histograms for each feature\n", "df.hist(figsize=(12, 8))\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Box plots for each feature\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "sns.boxplot(data=df, y='Age', ax=axes[0])\n", "sns.boxplot(data=df, y='Annual Income (k$)', ax=axes[1])\n", "sns.boxplot(data=df, y='Spending Score (1-100)', ax=axes[2])\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Scatter plots\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "sns.scatterplot(data=df, x='Age', y='Annual Income (k$)', ax=axes[0])\n", "sns.scatterplot(data=df, x='Age', y='Spending Score (1-100)', ax=axes[1])\n", "sns.scatterplot(data=df, x='Annual Income (k$)', y='Spending Score (1-100)', ax=axes[2])\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Correlation Analysis"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Correlation matrix\n", "corr_matrix = df.corr()\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', vmin=-1, vmax=1, center=0)\n", "plt.title('Correlation Matrix')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON> and Insights\n", "\n", "Based on the exploratory data analysis, we can draw the following insights:\n", "\n", "1. [Insight 1]\n", "2. [Insight 2]\n", "3. [Insight 3]\n", "\n", "These insights will help guide our customer segmentation analysis in the main script."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}